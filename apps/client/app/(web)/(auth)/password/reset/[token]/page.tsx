'use client';

import type { ResetPasswordSchema } from '@/app/(web)/(auth)/utils/validation';

import { useRouter, useParams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import Link from 'next/link';

import {
  FormMessage,
  FormControl,
  FormField,
  FormLabel,
  FormItem,
  Form,
} from '@ps/ui/components/form';
import { resetPasswordSchema } from '@/app/(web)/(auth)/utils/validation';
import { CardContent, Card } from '@ps/ui/components/card';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';
import API from '@/services/api';

type APIResponse = {
  status?: string;
  message?: string;
  token?: string;
};

type MutationVariables = ResetPasswordSchema & { token: string };

const resetPasswordAction = ({
  confirm_password: _,
  ...values
}: ResetPasswordSchema & { token: string }): Promise<{ status?: string; message?: string }> =>
  API.post<{ password: string; token: string }, { status?: string; message?: string }>(
    '/auth/password/reset',
    values,
  );

const ResetPasswordPage = () => {
  const { token } = useParams<{ token: string }>();
  const router = useRouter();

  const form = useForm<ResetPasswordSchema>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const { mutate, isSuccess } = useMutation<APIResponse, string, MutationVariables>({
    mutationFn: (values) => resetPasswordAction({ ...values, token }),
  });

  const onSubmit = (values: ResetPasswordSchema) => mutate({ ...values, token });

  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-md">
        <div className={cn('flex flex-col gap-6')}>
          <Card className="overflow-hidden p-0">
            <CardContent className="grid p-0 md:grid-cols-1">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 md:p-8">
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col items-center text-center">
                      <h1 className="text-2xl font-bold">
                        {isSuccess ? 'Password Reset Successful' : 'Set New Password'}
                      </h1>
                      <p className="text-muted-foreground text-balance">
                        {isSuccess
                          ? 'Your password has been reset successfully'
                          : 'Enter your new password below'}
                      </p>
                    </div>

                    {isSuccess ? (
                      <div className="space-y-4 text-center">
                        <p className="text-base leading-normal">Login using your new password.</p>
                        <Button className="w-full" onClick={() => router.push('/login')}>
                          Continue to Login
                        </Button>
                      </div>
                    ) : (
                      <>
                        <FormField
                          control={form.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>New Password</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="Enter new password"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="confirm_password"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Confirm New Password</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="Confirm new password"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={form.formState.isSubmitting}
                        >
                          {form.formState.isSubmitting ? 'Updating...' : 'Update Password'}
                        </Button>

                        <div className="text-center text-sm">
                          Remember your password?{' '}
                          <Link href="/login" className="underline underline-offset-4">
                            Back to Login
                          </Link>
                        </div>
                      </>
                    )}
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
          <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
            By clicking continue, you agree to our <a href="#">Terms of Service</a> and{' '}
            <a href="#">Privacy Policy</a>.
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;

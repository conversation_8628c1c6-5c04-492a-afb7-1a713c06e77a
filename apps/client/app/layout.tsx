import { <PERSON>ei<PERSON>_<PERSON>o, <PERSON><PERSON><PERSON> } from 'next/font/google';

import { getAppContext } from '@/providers/app/getAppContext';
import { Providers } from '@/components/providers';
import { auth } from '@/auth';
import { cn } from '@ps/ui/lib/utils';
import AppContextProvider from '@/providers/app/AppContextProvider';
import ReactQueryProvider from '@/providers/ReactQueryProvider';
import SessionProvider from '@/providers/SessionProvider';

import '@ps/ui/globals.css';

const fontSans = Geist({
  subsets: ['latin'],
  variable: '--font-sans',
});

const fontMono = Geist_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const appContext = await getAppContext();
  const session = await auth();

  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(fontSans.variable, fontMono.variable, 'font-sans antialiased')}>
        <AppContextProvider value={appContext}>
          <SessionProvider session={session} refetchOnWindowFocus={false} basePath="/api/auth">
            <ReactQueryProvider>
              <Providers>{children}</Providers>
            </ReactQueryProvider>
          </SessionProvider>
        </AppContextProvider>
      </body>
    </html>
  );
}

import type { NextMiddleware, NextRequest } from 'next/server';
import type { User } from '@ps/types';

import { NextResponse } from 'next/server';

import { auth } from '@/auth';

const protectedRoutes = [/^\/dashboard(?:\/.*)?$/, /^\/editor(?:\/.*)?$/];

const setCookieAndHeader = (response: NextResponse, cookieName: string, cookieValue: string) => {
  response.headers.set(`x-${cookieName}`, cookieValue);
  response.cookies.set(cookieName, cookieValue);
};

export const middleware = auth(async (request: NextRequest) => {
  const response = NextResponse.next();
  const newUrl = request.nextUrl;
  const auth = (request as unknown as { auth: { user: User; token: string } }).auth;
  const user = auth?.user;

  const { pathname, searchParams } = newUrl;

  // Set token if user is logged in
  const token = request.cookies.get('token')?.value;
  if (user && auth?.token && token !== auth?.token) {
    setCookieAndHeader(response, 'token', auth?.token);
  } else if (!user) {
    response.cookies.delete('token');
    response.cookies.delete('__Secure-authjs.session-token');
  }

  /* PROTECTED ROUTES SETTINGS -- START -- */
  const isLoginPage = pathname === '/login';
  const isProtectedRoute = protectedRoutes.some((route) => {
    if (route instanceof RegExp) {
      return route.test(pathname);
    }
    return pathname === route;
  });

  const url = '';

  // Check if user is logged in for protected routes
  if (isProtectedRoute && !user && !isLoginPage) {
    const redirectTo = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    newUrl.pathname = url ? `/${url}/login` : '/login';
    newUrl.search = new URLSearchParams({ redirectTo }).toString();
    return NextResponse.redirect(newUrl);
  }

  // Check if user has a valid subscription plan for protected routes
  if (isProtectedRoute && user && user.team && user.team.subscriptionPlan === 'no_plan') {
    newUrl.pathname = '/payment';
    return NextResponse.redirect(newUrl);
  }

  if (!isProtectedRoute && user) {
    const isResetTokenPasswordPage =
      /\/password\/reset/.test(newUrl.pathname) && newUrl.search.includes('token=');

    if (isResetTokenPasswordPage) newUrl.search = '';

    const isRestrictedPage =
      /\/login|\/signup|\/password\/forgot/.test(newUrl.pathname) || isResetTokenPasswordPage;

    if (isRestrictedPage) {
      newUrl.pathname = url ? `/${url}/` : '/';
      return NextResponse.redirect(newUrl);
    }
  }
  /* PROTECTED ROUTES SETTINGS -- END -- */

  return response;
}) as unknown as NextMiddleware;

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images - .svg, .png, .jpg, .jpeg, .gif, .webp
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!api|next-api|_next/static|_next/image|favicon.ico|.well-known|manifest.webmanifest|.*\\.(?:svg|png|jpg|jpeg|gif|webp|yaml|xml|txt)$).*)',
  ],
};

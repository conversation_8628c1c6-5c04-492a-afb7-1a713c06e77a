{"name": "@ps/server", "version": "0.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@bull-board/api": "^6.10.1", "@bull-board/express": "^6.10.1", "@bull-board/nestjs": "^6.10.1", "@keyv/redis": "^4.4.0", "@mailchimp/mailchimp_transactional": "^1.0.59", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@ps/common": "workspace:*", "@slack/web-api": "^7.9.2", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bullmq": "^5.54.2", "cache-manager": "^7.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "email-templates": "^12.0.3", "express-basic-auth": "^1.2.1", "geoip-lite": "^1.4.10", "mongodb": "^6.17.0", "mongoose": "^8.16.0", "node-cache": "^5.1.2", "nunjucks": "^3.2.4", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sass": "^1.89.2", "stripe": "^18.2.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@ps/eslint-config": "workspace:*", "@ps/types": "workspace:*", "@ps/typescript-config": "workspace:*", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.1", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/email-templates": "^10.0.4", "@types/express": "^5.0.3", "@types/geoip-lite": "^1.4.4", "@types/jest": "^30.0.0", "@types/mailchimp__mailchimp_transactional": "^1.0.11", "@types/node": "^24.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/preview-email": "^3.1.0", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "globals": "^16.2.0", "jest": "^30.0.0", "prettier": "^3.5.3", "preview-email": "^3.1.0", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0"}}